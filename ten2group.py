# coding = utf-8
"""
    @project: gkGpsRe
    @Author：Waite0603
    @file： ten2group.py
    @date：2025/2/28 下午9:20
    
    TODO:
"""
import pandas as pd

name = '河南省-18-2023-1-2.xlsx'

data = pd.read_excel(name)
print(data.head())

school_list = set(data['school_id'].tolist())

DATA = []

for i in school_list:
    one_school = data[data['school_id'] == i]

    # sg_name_list = list(set(one_school['sg_name'].tolist()))
    #
    # print(sg_name_list)
    # for j in sg_name_list:

    # 每 10 个专业为一组, 不足 10 个也为一组
    len_sg_name = len(one_school)

    for j in range(0, len_sg_name, 10):
        one_dict = {}
        # one_sg_name = one_school[one_school['sg_name'] == j].to_dict(orient='records')
        one_sg_name = one_school[j:j + 10].to_dict(orient='records')
        # 排序 把 min 为 '-'的放到最后
        one_sg_name = sorted(one_sg_name, key=lambda x: x['min'] == '-', reverse=False)
        # 根据 min 降序排序
        one_sg_name_not_ = [i for i in one_sg_name if i['min'] != '-']
        one_sg_name_have_ = [i for i in one_sg_name if i['min'] == '-']
        one_sg_name = sorted(one_sg_name_not_, key=lambda x: int(x['min']), reverse=True) + one_sg_name_have_
        print(one_sg_name)
        # print(one_sg_name)
        one_dict['school_id'] = i
        one_dict['school_name'] = one_sg_name[0]['school_name']
        one_dict['sg_name'] = j
        zhuanye_list = [i['spname'] for i in one_sg_name]
        one_dict['spname'] = '\n\t'.join(zhuanye_list)

        num_list = [str(i['num']) for i in one_sg_name]
        tuition_list = [str(i['tuition']) for i in one_sg_name]
        num_mix_tuition = [f"{num_list[i]}/{tuition_list[i]}" for i in range(len(num_list))]
        one_dict['num'] = '\n\t'.join(num_mix_tuition)

        min_2023_list = [str(i['min']) for i in one_sg_name]
        min_section_2023_list = [str(i['min_section']) for i in one_sg_name]
        min_values = [int(i) for i in min_2023_list if i != '-']

        if min_values:
            max_min = min(min_values)
        else:
            max_min = "-"
        min_mix_section = [f"{min_2023_list[i]}/{min_section_2023_list[i]}" for i in range(len(min_2023_list))]
        one_dict['min'] = '\n\t'.join(min_mix_section)
        one_dict['min_max'] = max_min

        min_2022_list = [str(i['min_2022']) for i in one_sg_name]
        min_section_2022_list = [str(i['min_section_2022']) for i in one_sg_name]
        min_mix_section_2022 = [f"{min_2022_list[i]}/{min_section_2022_list[i]}" for i in range(len(min_2022_list))]
        one_dict['min_2022'] = '\n\t'.join(min_mix_section_2022)

        min_2021_list = [str(i['min_2021']) for i in one_sg_name]
        min_section_2021_list = [str(i['min_section_2021']) for i in one_sg_name]
        min_mix_section_2021 = [f"{min_2021_list[i]}/{min_section_2021_list[i]}" for i in range(len(min_2021_list))]

        one_dict['min_2021'] = '\n\t'.join(min_mix_section_2021)

        DATA.append(one_dict)

# 根据min_max排序， ‘-’ 放到最后， 其他按照从大到小排序
# 分成两部分 一部分是有最低分的，一部分是没有最低分的
DATA1 = [i for i in DATA if i['min_max'] != '-']
DATA1 = sorted(DATA1, key=lambda x: int(x['min_max']), reverse=True)
DATA2 = [i for i in DATA if i['min_max'] == '-']
DATA = DATA1 + DATA2

DATA = pd.DataFrame(DATA)
DATA['province'] = '河南省'
DATA['kelei'] = 1
DATA['pici'] = 2
DATA['year'] = 2023
DATA.to_excel(f'111{name}', index=False)
