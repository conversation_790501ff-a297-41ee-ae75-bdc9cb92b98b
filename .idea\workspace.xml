<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8b95aaa8-b7f9-40a4-bdd5-26b731847327" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/2022_major.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/2022_major.py" beforeDir="false" afterPath="$PROJECT_DIR$/2022_major.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/2023_get_plan.py" beforeDir="false" afterPath="$PROJECT_DIR$/2023_get_plan.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/2023_major.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/2023_major.py" beforeDir="false" afterPath="$PROJECT_DIR$/2023_major.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/2024_get_plan.py" beforeDir="false" afterPath="$PROJECT_DIR$/2024_get_plan.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/2024_major.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/2024_major.py" beforeDir="false" afterPath="$PROJECT_DIR$/2024_major.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/2025_get_plan copy.py" beforeDir="false" afterPath="$PROJECT_DIR$/2025_get_plan copy.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/2025_get_plan.py" beforeDir="false" afterPath="$PROJECT_DIR$/2025_get_plan.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/format copy.py" beforeDir="false" afterPath="$PROJECT_DIR$/format copy.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/professional_group.py" beforeDir="false" afterPath="$PROJECT_DIR$/professional_group.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/recruit_plan_0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/recruit_plan_1.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/recruit_plan_2.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/安徽省-15-2024-1-2.xlsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/新版整合.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Waite0603&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Waite0603/gkGps.git&quot;,
    &quot;accountId&quot;: &quot;dab439d8-97bc-4743-a08c-609dbfe16381&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2ttpSujVF53z7K6unU9eTBpAjB5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.2021_major.executor": "Run",
    "Python.2023_major.executor": "Run",
    "Python.baidu_crawler.executor": "Run",
    "Python.format copy.executor": "Run",
    "Python.format.executor": "Run",
    "Python.getRealSID.executor": "Run",
    "Python.professional_group.executor": "Run",
    "Python.temporary_format.executor": "Run",
    "Python.try.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/workspace/tradeRoBot/backend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Waite0603\gkGpsShare" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Waite0603\gkGpsShare\getRealID\finish" />
    </key>
  </component>
  <component name="RunManager" selected="Python.baidu_crawler">
    <configuration name="baidu_crawler" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gkGps" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/baidu_crawler.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="format copy" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gkGps" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/format copy.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="getRealSID" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gkGps" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/getRealID" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/getRealID/getRealSID.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="temporary_format" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gkGpsShare" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/temporary_format.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="try" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="gkGps" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/try.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.baidu_crawler" />
        <item itemvalue="Python.try" />
        <item itemvalue="Python.format copy" />
        <item itemvalue="Python.getRealSID" />
        <item itemvalue="Python.temporary_format" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.25659.43" />
        <option value="bundled-python-sdk-181015f7ab06-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.25659.43" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8b95aaa8-b7f9-40a4-bdd5-26b731847327" name="更改" comment="" />
      <created>1741181229780</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741181229780</updated>
      <workItem from="1741181231238" duration="4767000" />
      <workItem from="1741351639094" duration="10155000" />
      <workItem from="1742562791554" duration="362000" />
      <workItem from="1743206142550" duration="31000" />
      <workItem from="1744039009422" duration="240000" />
      <workItem from="1748346136521" duration="1250000" />
      <workItem from="1748347415322" duration="2071000" />
      <workItem from="1750217606690" duration="1653000" />
      <workItem from="1750602996020" duration="1225000" />
      <workItem from="1750648359452" duration="54000" />
      <workItem from="1752545594830" duration="23266000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/gkGpsShare$temporary_format.coverage" NAME="temporary_format 覆盖结果" MODIFIED="1748348508232" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gkGpsShare$2023_major.coverage" NAME="2023_major 覆盖结果" MODIFIED="1748346291505" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gkGpsShare$format.coverage" NAME="format 覆盖结果" MODIFIED="1741488772881" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gkGps$try.coverage" NAME="try 覆盖结果" MODIFIED="1752576996296" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gkGps$baidu_crawler.coverage" NAME="baidu_crawler 覆盖结果" MODIFIED="1752631323874" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gkGps$getRealSID.coverage" NAME="getRealSID 覆盖结果" MODIFIED="1750226958921" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/getRealID" />
    <SUITE FILE_PATH="coverage/gkGpsShare$2021_major.coverage" NAME="2021_major 覆盖结果" MODIFIED="1741182662810" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gkGpsShare$professional_group.coverage" NAME="professional_group 覆盖结果" MODIFIED="1741402793409" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/gkGps$format_copy.coverage" NAME="format copy 覆盖结果" MODIFIED="1752545848048" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>