import pandas as pd
import difflib

def get_similarity(a: str, b: str) -> float:
    """计算两个字符串的相似度"""
    if pd.isna(a) or pd.isna(b):
        return 0.0
    return difflib.SequenceMatcher(None, str(a), str(b)).ratio()

def test_matching_logic():
    """测试专业名称匹配逻辑"""
    print("=== 测试专业名称匹配逻辑 ===")
    
    # 模拟数据
    test_data = pd.DataFrame({
        'school_id': [1, 1, 1],
        'spname': [
            '医学美容技术（因用人单位可能对考生身体素质有要求，请转氨酶异常的考生慎重报考。特征要求：不招色盲色弱）',
            '护理学（要求身高160cm以上）',
            '临床医学'
        ]
    })
    
    # 要匹配的专业名称
    test_names = [
        '医学美容技术（因用人单位可能对考生身体素质有要求，请转氨酶异常的考生慎重报考。特征要求;不招色盲色弱）',
        '医学美容技术',
        '护理学',
        '临床医学（五年制）'
    ]
    
    print("数据库中的专业名称:")
    for i, name in enumerate(test_data['spname']):
        print(f"  {i+1}. {name}")
    
    print("\n测试匹配:")
    
    for test_name in test_names:
        print(f"\n要匹配的名称: {test_name}")
        
        # 获取处理后的专业名称（去掉括号内容）
        spname = test_name.split('(')[0].strip() if '(' in test_name else test_name.strip()
        print(f"  清理后: {spname}")
        
        # 对数据库中的专业名称也进行同样的处理
        test_data_copy = test_data.copy()
        test_data_copy['spname_clean'] = test_data_copy['spname'].apply(
            lambda x: str(x).split('(')[0].strip() if pd.notna(x) and '(' in str(x) else str(x).strip()
        )
        
        print("  数据库清理后的名称:")
        for i, clean_name in enumerate(test_data_copy['spname_clean']):
            print(f"    {i+1}. {clean_name}")
        
        # 1. 完全匹配
        exact_match = test_data_copy[test_data_copy['spname_clean'] == spname]
        if not exact_match.empty:
            print(f"  ✓ 完全匹配成功: {exact_match.iloc[0]['spname']}")
            continue
        
        # 2. 模糊匹配
        similarities = test_data_copy['spname_clean'].apply(lambda x: get_similarity(spname, str(x)))
        if not similarities.empty:
            max_similarity = similarities.max()
            print(f"  最高相似度: {max_similarity:.3f}")
            if max_similarity >= 0.8:
                best_match_idx = similarities.idxmax()
                print(f"  ✓ 模糊匹配成功: {test_data_copy.loc[best_match_idx]['spname']}")
                continue
        
        # 3. 包含匹配
        contained_matches = test_data_copy[
            (test_data_copy['spname_clean'].str.contains(spname, na=False, regex=False)) |
            (test_data_copy['spname_clean'].apply(lambda x: spname in str(x) if pd.notna(x) else False))
        ]
        
        if not contained_matches.empty:
            print(f"  ✓ 包含匹配成功: {contained_matches.iloc[0]['spname']}")
            continue
        
        print("  ✗ 未找到匹配")

if __name__ == "__main__":
    test_matching_logic()
