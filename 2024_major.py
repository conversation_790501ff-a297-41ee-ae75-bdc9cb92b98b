import logging
import time
import random
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import pandas as pd
from tqdm import tqdm

from tools.response import get_major_score_line

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('2024_major.log'),
        logging.StreamHandler()
    ]
)


@dataclass
class Config:
    """Configuration settings for the application"""
    PROVINCE_ID: int = 41
    YEAR: int = 2024
    TYPE_ID: int = 0
    LOCAL_BATCH_ID: int = 0
    FILE_NAME: str = "getRealID/finish/河南专科匹配成功的数据.xlsx"
    MAX_RETRIES: int = 3
    RETRY_ATTEMPTS: int = 2
    PROXY: Dict[str, str] = None

    def __post_init__(self):
        self.PROXY = {
            'http': 'http://127.0.0.1:3060',
            'https': 'http://127.0.0.1:3060'
        }


class SchoolDataFetcher:
    """Handles fetching and processing of school data"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)

    def load_school_list(self) -> List[Dict[int, str]]:
        """Load school list from Excel file"""
        try:
            school_data = pd.read_excel(self.config.FILE_NAME)[['sid', 'school_name']]
            return [{int(i): j} for i, j in zip(school_data['sid'], school_data['school_name'])]
        except Exception as e:
            self.logger.error(f"Error loading school list: {str(e)}")
            raise

    def fetch_school_data(self, school_id: int, school_name: str) -> Tuple[List, bool]:
        """Fetch data for a single school with retry mechanism"""
        for attempt in range(self.config.MAX_RETRIES):
            try:
                self.logger.debug(f"Attempting to fetch data for {school_name} (attempt {attempt + 1})")
                res = get_major_score_line(
                    self.config.PROVINCE_ID,
                    self.config.YEAR,
                    school_id,
                    school_name
                )

                if res['code'] == 200:
                    return res['data'], True

                if res['code'] == 403:
                    sleep_time = random.uniform(10, 15)
                    self.logger.warning(f"403 error for {school_name}, waiting {sleep_time:.2f}s")
                else:
                    sleep_time = random.uniform(2, 5)
                    self.logger.warning(f"Error {res['code']} for {school_name}, waiting {sleep_time:.2f}s")

                time.sleep(sleep_time)

            except Exception as e:
                self.logger.error(f"Error fetching data for {school_name}: {str(e)}")
                time.sleep(random.uniform(2, 5))

        return [], False

    def process_school_list(self, school_list: List[Dict[int, str]]) -> Tuple[List, List]:
        """Process a list of schools and return successful data and failed schools"""
        data = []
        error_schools = []

        for school_info in tqdm(school_list, desc="Processing schools"):
            school_id = list(school_info.keys())[0]
            school_name = school_info[school_id]

            school_data, success = self.fetch_school_data(school_id, school_name)

            if success:
                data.extend(school_data)
            else:
                error_schools.append(school_info)
                self.logger.warning(f"Failed to fetch data for {school_name} (ID: {school_id})")

        return data, error_schools

    def save_data(self, data: List[Dict]) -> None:
        """Save processed data to Excel file"""
        filename = (f"{self.config.PROVINCE_ID}_{self.config.TYPE_ID}_"
                    f"{self.config.YEAR}_{self.config.LOCAL_BATCH_ID}_"
                    f"get_major_score_line.xlsx")

        try:
            pd.DataFrame(data).to_excel(filename, index=False)
            self.logger.info(f"Data saved successfully to {filename}")
        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")
            raise


def main():
    """Main execution function"""
    config = Config()
    fetcher = SchoolDataFetcher(config)

    try:
        # Load initial school list
        school_list = fetcher.load_school_list()
        all_data = []
        current_school_list = school_list

        # Process schools with retries
        for retry_count in range(config.RETRY_ATTEMPTS):
            if not current_school_list:
                break

            logging.info(f"Processing {len(current_school_list)} schools (Attempt {retry_count + 1})")
            data, error_schools = fetcher.process_school_list(current_school_list)
            all_data.extend(data)

            if not error_schools:
                break

            current_school_list = error_schools
            time.sleep(random.uniform(5, 10))

        # Log final results
        if error_schools:
            logging.warning(f"Failed to fetch data for {len(error_schools)} schools after all attempts")
            for school in error_schools:
                school_id = list(school.keys())[0]
                logging.warning(f"Failed school: {school[school_id]} (ID: {school_id})")

        # Save the results
        fetcher.save_data(all_data)

    except Exception as e:
        logging.error(f"Fatal error in main execution: {str(e)}")
        raise


if __name__ == "__main__":
    main()