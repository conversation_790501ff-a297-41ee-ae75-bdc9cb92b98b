# coding = utf-8
"""
    @project: gkGpsShare
    @Author：Waite0603
    @file： temporary_format.py
    @date：2025/5/27 下午7:58

    TODO:
"""
import pandas as pd

data =  pd.read_excel("41_0_2024_44_get_major_score_line.xlsx")



def get_school_data():
    """Get combined school data with both ID mappings and names"""
    print("\n正在获取学校数据...")
    school_df = pd.read_excel("getRealID/finish/河南本科匹配成功的数据.xlsx")
    return school_df[['sid', 'old_sid', 'school_name']].to_dict('records')


school_data = get_school_data()

print(data.columns)
data = data[['school_id', 'school_name', 'sg_name', 'spname', 'type_id', 'min',  'min', 'min_section']]

print(data.head())

# 根据学校名称匹配真实 school_id
def match_school_id(df: pd.DataFrame, school_data: list) -> pd.DataFrame:
    """Match school_id with real school IDs from the provided data."""
    school_dict = {school['school_name']: school['old_sid'] for school in school_data}
    df['school_id'] = df['school_name'].map(school_dict)
    return df.dropna(subset=['school_id'])

data = match_school_id(data, school_data)

print(data.head())

data = data[(data.type_id == "2_7_0") | (data.type_id == "2_8_0")]

data.to_excel("22222.xlsx", index=False)