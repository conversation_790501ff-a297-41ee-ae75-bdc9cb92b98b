from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import re
import pandas as pd


def format_text(text):
    """
    格式化文本：
    1. 删除所有方括号及其中的内容
    2. 在段落之间添加两个空行

    Args:
        text: 原始文本

    Returns:
        str: 格式化后的文本
    """
    # 删除所有方括号及其中的内容
    text = re.sub(r'\[[^\]]*\]', '', text)

    # 分割段落并在之间添加两个空行
    paragraphs = text.split('\n')
    formatted_text = ''
    for paragraph in paragraphs:
        formatted_text += "    " + paragraph.strip() + "\n"

    return formatted_text


def get_baidu_summary(url):
    """
    使用Selenium获取百度百科词条摘要
    
    Args:
        url: 百度百科词条URL
        
    Returns:
        str: 词条摘要文本
    """
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')

    try:
        # 创建Chrome浏览器实例
        driver = webdriver.Chrome(options=chrome_options)

        # 设置页面加载超时时间
        driver.set_page_load_timeout(10)

        # 访问URL
        driver.get(url)

        # 等待目标元素加载
        wait = WebDriverWait(driver, 10)
        summary_element = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "lemmaSummary_iVs1B"))
        )

        # 获取文本内容并格式化
        summary_text = summary_element.text
        formatted_text = format_text(summary_text)

        return formatted_text

    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

    finally:
        # 确保关闭浏览器
        if 'driver' in locals():
            driver.quit()


if __name__ == "__main__":
    # # 测试URL
    # url = "https://baike.baidu.com/item/%E6%B2%B3%E5%8C%97%E5%B7%A5%E4%B8%9A%E5%A4%A7%E5%AD%A6"
    #
    # # 获取摘要
    # summary = get_baidu_summary(url)
    #
    # if summary:
    #     print("词条摘要：")
    #     print(summary)
    # else:
    #     print("获取摘要失败")

    data = pd.read_excel("baidu_summary_results13412.xlsx", dtype=str)
    print(data.columns)
    data_dict = data.to_dict('records')
    # school_list = data['院校名称'].tolist()
    # print(school_list)
    print(data_dict)
    DATA_DICT = {}
    for item in data_dict:
        school_name = item['学校名称']
        if school_name not in DATA_DICT:
            DATA_DICT[school_name] = item['摘要']
    print(DATA_DICT)

    num = 1
    for school in DATA_DICT:
        # nan
        if pd.isna(DATA_DICT[school]):
            print(f"学校 {school}，摘要为空")

            url = f"https://baike.baidu.com/item/{school}"
            summary = get_baidu_summary(url)
            print(summary)

            if summary:
                DATA_DICT[school] = summary
            else:
                DATA_DICT[school] = ""
            num += 1
            time.sleep(5)  # 避免请求过于频繁

    results = []
    # 重构DATA_DICT
    for school, summary in DATA_DICT.items():
        if summary:
            results.append({
                "学校名称": school,
                "摘要": summary,
                "前世今生": summary.split('\n')[1]
            })
        else:
            results.append({
                "学校名称": school,
                "摘要": "",
                "前世今生": "",
            })

    # 保存结果到Excel
    result_df = pd.DataFrame(results)
    result_df.to_excel("baidu_summary_result.xlsx", index=False)
    print("摘要已保存到 baidu_summary_result.xlsx")
