# coding = utf-8
"""
    @project: gkGpsRe
    @Author：Waite0603
    @file： spilt.py
    @date：2024/9/7 下午10:21
    
    TODO:
"""


def spilt(profession):
  try:
    special_list = ['中外合作办学', '5+3一体化', '本硕', '本博', '本硕博']
    text = profession
    profession_list = profession.split("（")
    profession_name = profession_list[0]

    flag = False
    for i in profession_list:
      for j in special_list:
        if j in i:
          text = profession_name + "（" + i
          flag = True

    if not flag:
      if "）" in profession:
        text = profession.split("）")[0] + "）"
      elif "。" in profession:
        text = profession.split("。")[0] + "。"
      elif "；" in profession:
        text = profession.split("；")[0] + "；"

      if "办学地点" in text:
        text_list = text.split("办学地点")
        if text_list[0][-1] != "（":
          text = text_list[0][:-1] + "）"

    if len(text) > 50:
      text = text[:49]

    text = text.replace("（", "(").replace("）", ")")

    return text
  except:
    return profession