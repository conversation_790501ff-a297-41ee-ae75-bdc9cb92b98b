# -*- coding: utf-8 -*-
"""
@author: Waite  
@time: 2023-04-30 23:04
@file: auth.py
@desc:

:param: local_batch_id: 批次, 14 本科批
:param: local_province_id: 省份, 44 广东
:param: local_type_id: 科类, 2073 物理类, 2074 历史类
:param: page: 页数, 1
:param: school_id: 学校id, 102 厦门大学
:param: size: 每页数量, 10
:param: special_group: 特殊类型, 为空
:param: uri: 接口地址, apidata/api/gk/score/special
:param: year: 年份, 2021
:param: signsafe: 加密参数

"""
import base64
import hashlib
import hmac
import re
import urllib.parse


# sign_safe 加密
def url_encrypt(url):
    """
    :param url: "https://api.eol.cn/web/api/?local_batch_id=14&local_province_id=44&local_type_id=2073&page=1
        &school_id=102&size=10&special_group=&uri=apidata/api/gk/score/special&year=2021"
    :return: signsafe
    """
    url = re.sub(r'^\/|https?:\/\/\/?', '', url)
    url = urllib.parse.unquote(url)

    key = b"D23ABC@#56"
    sign_safe = hmac.new(key, url.encode('utf-8'), hashlib.sha1)
    sign_safe = base64.b64encode(sign_safe.digest()).decode('utf-8')

    sign_safe = hashlib.md5(sign_safe.encode('utf-8')).hexdigest()
    return sign_safe