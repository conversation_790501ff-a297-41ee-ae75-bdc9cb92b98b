# -*- coding: utf-8 -*-
"""
@author: Waite  
@time: 2023-05-09 17:28
@file: response.py
@desc:

TODO: 请求接口
"""
import random
import time

import requests

from tools.auth import url_encrypt
from tools.util import get_headers

# 代理设置, 开启本地代理
proxy = {
  'http': 'http://127.0.0.1:3060',
  'https': 'http://127.0.0.1:3060'
}


# 专业分数线
def get_professional_score_line(province_id, year, type_id, school_id, local_batch_id):
  """
  :param local_batch_id: 本科一批/本科二批/专科批
  :param province_id: 省份 id
  :param year: 年份
  :param type_id: 理科/文科
  :param school_id: 学校 id
  :return: {code: 200, data: [], num: 0, msg: "成功"}
  """
  score_data = []
  num_found = 0
  try:
    for page in range(1, 100):
      # 7 - 本科一批
      # 10 - 专科批
      url = (f"https://api.zjzw.cn/web/api/?local_batch_id={local_batch_id}&"
             f"local_province_id={province_id}&local_type_id="
             f"{type_id}&page={page}&school_id={school_id}&size=10"
             f"&special_group=&uri=apidata/api/gk/score/special&year={year}")
      # 加密参数
      signsafe = url_encrypt(url)
      url += f"&signsafe={signsafe}"

      # 请求接口
      res = requests.get(url, headers=get_headers(), proxies=proxy, timeout=10).json()

      # 频繁检测
      if res['code'] == '1069':
        return {
          "code": 403,
          "data": [],
          "num": 0,
          "msg": "请求过于频繁, 请稍后再试"
        }

      # 查看是否有数据
      if not res['data']['item']:
        break

      # 保存数据
      score_data.extend(res['data']['item'])
      num_found = res['data']['numFound']

      # time sleep 0.3 - 1
      time.sleep(random.uniform(0.3, 1))

    # 数据校验
    if len(score_data) != num_found:
      return {
        "code": 500,
        "data": [],
        "num": 0,
        "msg": "数据校验失败"
      }

    return {
      "code": 200,
      "data": score_data,
      "num": num_found,
      "msg": "成功"
    }
  except Exception as e:
    return {
      "code": 500,
      "data": [],
      "num": 0,
      "msg": f"请求失败-{e}"
    }


# 招生计划
def get_recruit_plan(province_id, year, type_id, school_id, local_batch_id):
  """
  :param province_id: 省份 id
  :param year: 年份
  :param type_id: 理科/文科
  :param school_id: 学校 id
  :return: {code: 200, data: [], num: 0, msg: "成功"}
  """
  recruit_data = []
  num_found = 0

  try:
    for page in range(1, 100):
      # https://api.zjzw.cn/web/api/?local_batch_id=7&local_province_id=45&local_type_id=2&page=1&school_id=140&size=10&special_group=&uri=apidata/api/gkv3/plan/school&year=2023&signsafe=5e7f01144f4f5495088a9247372bb70b
      # url = (
      #     f"https://api.zjzw.cn/web/api/?e_sort=zslx_rank,min&e_sorttype=desc,desc&local_province_id={province_id}&local_type_id={type_id}"
      #     f"&page={page}&school_id={school_id}&size=10&special_group="
      #     f"&uri=apidata/api/gkv3/plan/school&year={year}")

      url = (
        f"https://api.zjzw.cn/web/api/?local_batch_id={local_batch_id}&local_province_id={province_id}"
        f"&local_type_id={type_id}&page={page}&school_id={school_id}&size=10"
        f"&special_group=&uri=apidata/api/gkv3/plan/school&year={year}"
      )

      # 加密参数
      signsafe = url_encrypt(url)
      url += f"&signsafe={signsafe}"

      # 请求接口
      res = requests.get(url, headers=get_headers(), proxies=proxy, timeout=10).json()

      # 网站频繁检测
      if res['code'] == '1069':
        return {
          "code": 403,
          "data": [],
          "num": 0,
          "msg": "请求过于频繁, 请稍后再试"
        }

      # 查看是否有数据
      if not res['data']['item']:
        break

      # 保存数据
      recruit_data.extend(res['data']['item'])
      num_found = res['data']['numFound']

      # time sleep 0.3 - 1
      time.sleep(random.uniform(0.3, 1) + 1)

    # 数据校验
    if len(recruit_data) != num_found:
      return {
        "code": 500,
        "data": [],
        "num": 0,
        "msg": "数据校验失败"
      }

    # 返回数据
    return {
      "code": 200,
      "data": recruit_data,
      "num": num_found,
      "msg": "成功"
    }
  except Exception as e:
    return {
      "code": 500,
      "data": [],
      "num": 0,
      "msg": f"请求失败-{e}"
    }


# print(get_recruit_plan(44, 2024, 2074, 102, 14))


# 各省分数线
def get_province_score_line(province_id, year, type_id, school_id):
  """
  :param province_id: 省份 id
  :param year: 年份
  :param type_id: 理科/文科
  :param school_id: 学校 id
  :return: {code: 200, data: [], num: 0, msg: "成功"}
  """
  province_score_data = []
  num_found = 0

  try:
    for page in range(1, 100):
      # https://api.zjzw.cn/web/api/?e_sort=zslx_rank,min&e_sorttype=desc,desc&local_province_id=41&local_type_id=1&page=1&school_id=102&size=10&uri=apidata/api/gk/score/province&year=2023&signsafe=7b94fa0dfecdbe2ffe48c8ccaeb6df7e
      url = (f"https://api.eol.cn/web/api/?e_sort=zslx_rank,min"
             f"&e_sorttype=desc,desc&local_province_id={province_id}"
             f"&local_type_id={type_id}&page={page}&school_id={school_id}"
             f"&size=10&uri=apidata/api/gk/score/province&year={year}")
      # url = (
      #   f"https://api.zjzw.cn/web/api/?e_sort=zslx_rank,min&e_sorttype=desc,desc&local_province_id={province_id}"
      #   f"&local_type_id={type_id}&page={page}&school_id={school_id}&size=10&uri=apidata/api/gk/score/province&year=2023&signsafe=7b94fa0dfecdbe2ffe48c8ccaeb6df7e")
      # 加密参数
      signsafe = url_encrypt(url)
      url += f"&signsafe={signsafe}"

      # 请求接口
      res = requests.get(url, headers=get_headers(), proxies=proxy, timeout=10).json()

      # 网站频繁检测
      if res['code'] == '1069':
        return {
          "code": 403,
          "data": [],
          "num": 0,
          "msg": "请求过于频繁, 请稍后再试"
        }

      # 查看是否有数据
      if not res['data']['item']:
        break

      # 保存数据
      province_score_data.extend(res['data']['item'])
      num_found = res['data']['numFound']

      # time sleep 0.3 - 1
      # time.sleep(random.uniform(0.3, 1))

    # 数据校验
    if len(province_score_data) != num_found:
      return {
        "code": 500,
        "data": [],
        "num": 0,
        "msg": "数据校验失败"
      }

    # 返回数据
    return {
      "code": 200,
      "data": province_score_data,
      "num": num_found,
      "msg": "成功"
    }
  except Exception as e:
    return {
      "code": 500,
      "data": [],
      "num": 0,
      "msg": f"请求失败-{e}"
    }


# 专业分数线 新接口
def get_major_score_line(province_id, year, school_id, school_name):
  """
  :param province_id:
  :param year:
  :param school_id:
  :param school_name: 学校名称
  :return:
  """
  data_json = []
  url = f"https://static-data.gaokao.cn/www/2.0/schoolspecialscore/{school_id}/{year}/{province_id}.json"
  try:
    res = requests.get(url, headers=get_headers(), proxies=proxy, timeout=10).json()
    for i in res['data']:
      for j in res['data'][i]['item']:
        j['school_name'] = school_name
        j['type_id'] = i
        data_json.append(j)

    return {
      "code": 200,
      "data": data_json
    }
  except requests.exceptions.JSONDecodeError:
    return {
      "code": 200,
      "data": []
    }
  except Exception as e:
    print(e)
    return {
      "code": 500,
      "data": [],
      "num": 0,
      "msg": "请求失败"
    }


# 招生计划 新接口
def get_recruit_plan_new(province_id, year, school_id, school_name):
  """
  :param province_id: 省份 id
  :param year: 年份
  :param school_id: 学校 id
  :param school_name: 学校名称
  :return: {code: 200, data: [], num: 0, msg: "成功"}
  """
  data_json = []
  # https://static-data.gaokao.cn/www/2.0/schoolspecialplan/
  url = f"https://static-data.gaokao.cn/www/2.0/schoolspecialplan/{school_id}/{year}/{province_id}.json"
  try:
    res = requests.get(url, headers=get_headers(), proxies=proxy, timeout=10).json()
    for i in res['data']:
      for j in res['data'][i]['item']:
        j['school_name'] = school_name
        j['type_id'] = i
        data_json.append(j)

    return {
      "code": 200,
      "data": data_json
    }
  except requests.exceptions.JSONDecodeError:
    return {
      "code": 200,
      "data": []
    }
  except Exception as e:
    print(e)
    return {
      "code": 500,
      "data": [],
      "num": 0,
      "msg": "请求失败"
    }


# print(get_recruit_plan_new(41, 2024, 102, "厦门大学"))


# 各省分数线 新接口
def get_province_score_line_new(province_id, year, school_id, school_name):
  """
  :param province_id: 省份 id
  :param year: 年份
  :param school_id: 学校 id
  :param school_name: 学校名称
  :return: {code: 200, data: [], num: 0, msg: "成功"}
  """
  data_json = []
  url = f"https://static-data.gaokao.cn/www/2.0/schoolprovincescore/{school_id}/{year}/{province_id}.json"
  # print(url)
  try:
    res = requests.get(url, headers=get_headers(), proxies=proxy, timeout=10).json()
    for i in res['data']:
      for j in res['data'][i]['item']:
        j['school_name'] = school_name
        j['type_id'] = i
        data_json.append(j)

    return {
      "code": 200,
      "data": data_json
    }
  except requests.exceptions.JSONDecodeError:
    return {
      "code": 200,
      "data": []
    }
  except Exception as e:
    print(e)
    return {
      "code": 500,
      "data": [],
      "num": 0,
      "msg": "请求失败"
    }

# print(get_province_score_line_new(36, 2023, 102, "厦门大学"))
