import pandas as pd
import requests
import json

# 读取数据
data = pd.read_excel('1.xlsx')
data_school = pd.read_excel('./getRealID/finish/内蒙古本科匹配成功的数据.xlsx').to_dict('records')

# 第一步：取出 data 的 school_id 和 school_name
schools_to_match = data[['school_id', 'school_name']].drop_duplicates()

# 创建映射字典用于快速查找
# 按 old_sid 映射
sid_mapping = {item['old_sid']: item['sid'] for item in data_school}
# 按 school_name 映射
name_mapping = {item['school_name']: item['sid'] for item in data_school}

# 存储匹配结果
matched_schools = []
failed_matches = []

# 遍历需要匹配的学校
for _, row in schools_to_match.iterrows():
    school_id = str(row['school_id'])  # 转换为字符串以匹配 old_sid
    school_name = row['school_name']

    matched_sid = None
    match_method = None

    # 首先根据 school_id（old_sid）匹配
    if school_id in sid_mapping:
        matched_sid = sid_mapping[school_id]
        match_method = 'by_id'
    # 如果拿不到，根据名字匹配
    elif school_name in name_mapping:
        matched_sid = name_mapping[school_name]
        match_method = 'by_name'
    # 如果还是拿不到，把"大学"换成"学院"再试一次
    elif '大学' in school_name:
        modified_name = school_name.replace('大学', '学院')
        if modified_name in name_mapping:
            matched_sid = name_mapping[modified_name]
            match_method = 'by_name_modified'
    # 如果原来是"学院"，试试换成"大学"
    elif '学院' in school_name:
        modified_name = school_name.replace('学院', '大学')
        if modified_name in name_mapping:
            matched_sid = name_mapping[modified_name]
            match_method = 'by_name_modified'

    # 记录结果
    if matched_sid is not None:
        matched_schools.append({
            'original_school_id': school_id,
            'school_name': school_name,
            'matched_sid': matched_sid,
            'match_method': match_method
        })
    else:
        failed_matches.append({
            'original_school_id': school_id,
            'school_name': school_name
        })

# 输出结果
print("=== 匹配成功的学校 ===")
print(f"成功匹配 {len(matched_schools)} 所学校")

# 按匹配方式分组显示
by_id = [s for s in matched_schools if s['match_method'] == 'by_id']
by_name = [s for s in matched_schools if s['match_method'] == 'by_name']
by_name_modified = [s for s in matched_schools if s['match_method'] == 'by_name_modified']

print(f"\n通过ID匹配成功: {len(by_id)} 所")
for school in by_id:
    print(f"  ID: {school['original_school_id']}, 名称: {school['school_name']}, SID: {school['matched_sid']}")

print(f"\n通过名称匹配成功: {len(by_name)} 所")
for school in by_name:
    print(f"  ID: {school['original_school_id']}, 名称: {school['school_name']}, SID: {school['matched_sid']}")

print(f"\n通过修改名称匹配成功: {len(by_name_modified)} 所")
for school in by_name_modified:
    print(f"  ID: {school['original_school_id']}, 名称: {school['school_name']}, SID: {school['matched_sid']}")

print("\n=== 匹配失败的学校列表 ===")
print(f"匹配失败 {len(failed_matches)} 所学校")
for school in failed_matches:
    print(f"ID: {school['original_school_id']}, 名称: {school['school_name']}")

# 创建匹配结果的DataFrame，方便后续使用
if matched_schools:
    matched_df = pd.DataFrame(matched_schools)
    print(f"\n匹配成功率: {len(matched_schools)}/{len(schools_to_match)} = {len(matched_schools)/len(schools_to_match)*100:.2f}%")
else:
    matched_df = pd.DataFrame()
    print("\n没有匹配成功的学校")

# 对匹配失败的学校，尝试从 school_dic_name.json 中匹配
print("\n=== 尝试从 school_dic_name.json 中匹配失败的学校 ===")

# 读取 school_dic_name.json
with open('data/school_dic_name.json', 'r', encoding='utf-8') as f:
    school_dic_name = json.load(f)

# 存储第二轮匹配结果
second_round_matched = []
final_failed = []

for school in failed_matches:
    school_id = school['original_school_id']
    school_name = school['school_name']

    matched_sid = None
    match_method = None

    # 直接按名字匹配
    if school_name in school_dic_name:
        matched_sid = school_dic_name[school_name]
        match_method = 'by_name_dic'
    # 把"大学"换成"学院"再试一次
    elif '大学' in school_name:
        modified_name = school_name.replace('大学', '学院')
        if modified_name in school_dic_name:
            matched_sid = school_dic_name[modified_name]
            match_method = 'by_name_dic_modified'
    # 如果原来是"学院"，试试换成"大学"
    elif '学院' in school_name:
        modified_name = school_name.replace('学院', '大学')
        if modified_name in school_dic_name:
            matched_sid = school_dic_name[modified_name]
            match_method = 'by_name_dic_modified'

    # 记录结果
    if matched_sid is not None:
        second_round_matched.append({
            'oid': school_id,  # original id
            'sid': matched_sid,
            'school_name': school_name,
            'match_method': match_method
        })
    else:
        final_failed.append({
            'oid': school_id,
            'school_name': school_name
        })

# 输出第二轮匹配结果
print(f"第二轮匹配成功: {len(second_round_matched)} 所学校")
for school in second_round_matched:
    print(f"  OID: {school['oid']}, 名称: {school['school_name']}, SID: {school['sid']}, 方式: {school['match_method']}")

print(f"\n最终匹配失败: {len(final_failed)} 所学校")
for school in final_failed:
    print(f"  OID: {school['oid']}, 名称: {school['school_name']}")

# 保存所有匹配结果到Excel文件
if matched_schools:
    matched_df = pd.DataFrame(matched_schools)
    matched_df.to_excel('matched_schools_first_round.xlsx', index=False)
else:
    matched_df = pd.DataFrame()

# 保存第二轮匹配结果
if second_round_matched:
    second_round_df = pd.DataFrame(second_round_matched)
    second_round_df.to_excel('matched_schools_second_round.xlsx', index=False)

# 保存最终匹配失败的学校
if final_failed:
    final_failed_df = pd.DataFrame(final_failed)
    final_failed_df.to_excel('final_failed_matches.xlsx', index=False)

# 合并所有成功匹配的结果
all_matched = []
# 第一轮匹配结果
for school in matched_schools:
    all_matched.append({
        'oid': school['original_school_id'],
        'sid': school['matched_sid'],
        'school_name': school['school_name'],
        'match_source': 'first_round',
        'match_method': school['match_method']
    })

# 第二轮匹配结果
for school in second_round_matched:
    all_matched.append({
        'oid': school['oid'],
        'sid': school['sid'],
        'school_name': school['school_name'],
        'match_source': 'second_round',
        'match_method': school['match_method']
    })

# 保存所有匹配结果
if all_matched:
    all_matched_df = pd.DataFrame(all_matched)
    all_matched_df.to_excel('all_matched_schools.xlsx', index=False)
    print(f"\n总计匹配成功: {len(all_matched)} 所学校")
    print(f"总匹配成功率: {len(all_matched)}/{len(schools_to_match)} = {len(all_matched)/len(schools_to_match)*100:.2f}%")