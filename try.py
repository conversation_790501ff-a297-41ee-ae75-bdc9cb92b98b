import pandas as pd
import requests
import json

# 读取数据
data = pd.read_excel('1.xlsx')
data_school = pd.read_excel('./getRealID/finish/内蒙古本科匹配成功的数据.xlsx').to_dict('records')

# 第一步：取出 data 的 school_id 和 school_name
schools_to_match = data[['school_id', 'school_name']].drop_duplicates()

# 创建映射字典用于快速查找
# 按 old_sid 映射
sid_mapping = {item['old_sid']: item['sid'] for item in data_school}
# 按 school_name 映射
name_mapping = {item['school_name']: item['sid'] for item in data_school}

# 存储匹配结果
matched_schools = []
failed_matches = []

# 遍历需要匹配的学校
for _, row in schools_to_match.iterrows():
    school_id = str(row['school_id'])  # 转换为字符串以匹配 old_sid
    school_name = row['school_name']

    matched_sid = None
    match_method = None

    # 首先根据 school_id（old_sid）匹配
    if school_id in sid_mapping:
        matched_sid = sid_mapping[school_id]
        match_method = 'by_id'
    # 如果拿不到，根据名字匹配
    elif school_name in name_mapping:
        matched_sid = name_mapping[school_name]
        match_method = 'by_name'

    # 记录结果
    if matched_sid is not None:
        matched_schools.append({
            'original_school_id': school_id,
            'school_name': school_name,
            'matched_sid': matched_sid,
            'match_method': match_method
        })
    else:
        failed_matches.append({
            'original_school_id': school_id,
            'school_name': school_name
        })

# 输出结果
print("=== 匹配成功的学校 ===")
print(f"成功匹配 {len(matched_schools)} 所学校")
for school in matched_schools:
    print(f"ID: {school['original_school_id']}, 名称: {school['school_name']}, "
          f"匹配到SID: {school['matched_sid']}, 匹配方式: {school['match_method']}")

print("\n=== 匹配失败的学校列表 ===")
print(f"匹配失败 {len(failed_matches)} 所学校")
for school in failed_matches:
    print(f"ID: {school['original_school_id']}, 名称: {school['school_name']}")

# 创建匹配结果的DataFrame，方便后续使用
if matched_schools:
    matched_df = pd.DataFrame(matched_schools)
    print(f"\n匹配成功率: {len(matched_schools)}/{len(schools_to_match)} = {len(matched_schools)/len(schools_to_match)*100:.2f}%")
else:
    matched_df = pd.DataFrame()
    print("\n没有匹配成功的学校")