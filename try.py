from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from concurrent.futures import ThreadPoolExecutor
import threading
import time
import re
import pandas as pd

# 添加信号量控制并发
semaphore = threading.Semaphore(3)  # 最多3个并发请求


def format_text(text):
    """
    格式化文本：
    1. 删除所有方括号及其中的内容
    2. 在段落之间添加两个空行

    Args:
        text: 原始文本

    Returns:
        str: 格式化后的文本
    """
    # 删除所有方括号及其中的内容
    text = re.sub(r'\[[^\]]*\]', '', text)

    # 分割段落并在之间添加两个空行
    paragraphs = text.split('\n')
    formatted_text = ''
    for paragraph in paragraphs:
        formatted_text += "    " + paragraph.strip() + "\n"

    return formatted_text


def get_baidu_summary(url):
    """
    使用Selenium获取百度百科词条摘要

    Args:
        url: 百度百科词条URL

    Returns:
        str: 词条摘要文本
    """
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')

    try:
        # 创建Chrome浏览器实例
        driver = webdriver.Chrome(options=chrome_options)

        # 设置页面加载超时时间
        driver.set_page_load_timeout(10)

        # 访问URL
        driver.get(url)

        # 等待目标元素加载
        wait = WebDriverWait(driver, 10)
        summary_element = wait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "lemmaSummary_iVs1B"))
        )

        # 获取文本内容并格式化
        summary_text = summary_element.text
        formatted_text = format_text(summary_text)

        return formatted_text

    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

    finally:
        # 确保关闭浏览器
        if 'driver' in locals():
            driver.quit()


def crawl_school(school, num, total):
    """单个学校的爬取任务"""
    with semaphore:
        print(f"正在处理：{school}", str(num), "/" + str(total))
        url = f"https://baike.baidu.com/item/{school}"
        summary = get_baidu_summary(url)
        print(f"完成：{school}")

        result = {
            "学校名称": school,
            "摘要": summary if summary else ""
        }
        time.sleep(5)  # 降低等待时间
        return result


def main():
    data = pd.read_excel("2025.7.14.xlsx", dtype=str, sheet_name=[2])
    print(data)
    school_list = data[2]['院校名称'].tolist()
    print(school_list)

    results = []
    total = len(school_list)

    # 使用线程池处理
    with ThreadPoolExecutor(max_workers=3) as executor:
        # 提交所有任务
        future_to_school = {
            executor.submit(crawl_school, school, i + 1, total): school
            for i, school in enumerate(school_list)
        }

        # 获取结果
        for future in future_to_school:
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                school = future_to_school[future]
                print(f"处理学校 {school} 时出错: {str(e)}")
                results.append({
                    "学校名称": school,
                    "摘要": ""
                })

    # 保存结果到Excel
    result_df = pd.DataFrame(results)
    result_df.to_excel("baidu_summary_results13412.xlsx", index=False)
    print("摘要已保存到 baidu_summary_results13412.xlsx")


if __name__ == "__main__":
    main()
