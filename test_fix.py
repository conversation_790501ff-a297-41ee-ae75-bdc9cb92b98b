import pandas as pd

# 测试基本功能
print("测试开始...")

try:
    # 测试加载学校数据
    school_data = pd.read_excel("getRealID/finish/内蒙古本科匹配成功的数据.xlsx")
    print(f"学校数据加载成功，共 {len(school_data)} 条记录")
    print("列名:", school_data.columns.tolist())
    
    # 测试加载2025年数据
    data_2025 = pd.read_excel("1.xlsx")
    print(f"2025年数据加载成功，共 {len(data_2025)} 条记录")
    print("列名:", data_2025.columns.tolist())
    
    # 检查是否有 sp_info 列
    if 'sp_info' not in data_2025.columns:
        print("警告：2025年数据中没有 sp_info 列，将创建空列")
        data_2025['sp_info'] = ''
    
    # 测试过滤
    type_ids = ['2073_14_0','1_8_0', '1_7_0', '1_51_0', '1_44_0', '2073_3453_0', '1_150_0', '1_111_0', '2073_46_0', '2073_47_0',  '2073_14_0']
    filtered_data = data_2025[data_2025['type_id'].isin(type_ids)]
    print(f"过滤后数据：{len(filtered_data)} 条记录")
    
    # 检查数据类型
    print(f"\n数据类型检查:")
    print(f"school_data['old_sid'] 类型: {school_data['old_sid'].dtype}")
    print(f"data_2025['school_id'] 类型: {data_2025['school_id'].dtype}")
    print(f"school_data['old_sid'] 样本: {school_data['old_sid'].head().tolist()}")
    print(f"data_2025['school_id'] 样本: {data_2025['school_id'].head().tolist()}")

    # 测试学校ID匹配 - 确保类型一致
    school_dict = {str(row['old_sid']): row['sid'] for row in school_data.to_dict('records')}
    print(f"学校ID映射字典创建成功，共 {len(school_dict)} 个映射")

    # 测试几个学校ID
    sample_school_ids = filtered_data['school_id'].unique()[:5]
    print("\n测试学校ID匹配:")
    for sid in sample_school_ids:
        sid_str = str(sid)
        if sid_str in school_dict:
            print(f"  {sid} -> {school_dict[sid_str]} (匹配成功)")
        else:
            print(f"  {sid} -> 未找到匹配")
    
    print("\n测试完成！")
    
except Exception as e:
    print(f"测试出错: {str(e)}")
    import traceback
    traceback.print_exc()
