import pandas as pd
from typing import Dict

# 简化版本测试学校匹配逻辑

def test_school_matching():
    print("=== 测试学校匹配逻辑 ===")
    
    # 加载数据
    school_data = pd.read_excel("getRealID/finish/内蒙古本科匹配成功的数据.xlsx")
    data_2025 = pd.read_excel("1.xlsx")
    
    # 创建学校字典
    school_dict = {str(row['old_sid']): row['sid'] for row in school_data.to_dict('records')}
    
    # 测试数据
    type_ids = ['2073_14_0','1_8_0', '1_7_0', '1_51_0', '1_44_0', '2073_3453_0', '1_150_0', '1_111_0', '2073_46_0', '2073_47_0',  '2073_14_0']
    filtered_data = data_2025[data_2025['type_id'].isin(type_ids)]
    
    # 只取前10条数据进行测试
    test_data = filtered_data.head(10).to_dict('records')
    
    print(f"测试数据：{len(test_data)} 条记录")
    
    # 测试匹配逻辑
    school_not_found = []
    school_name_mapping = {}
    unknown_school_counter = 10000
    
    for item in test_data:
        original_sid = item['school_id']
        sid_str = str(original_sid)
        school_name = item.get('school_name', '')
        
        print(f"\n处理学校: {original_sid} ({school_name})")
        
        if sid_str in school_dict:
            mapped_sid = school_dict[sid_str]
            print(f"  ID匹配成功: {original_sid} -> {mapped_sid}")
        else:
            # 尝试按照学校名字匹配
            name_found = False
            if school_name:
                for school_record in school_data.to_dict('records'):
                    if school_record['school_name'] == school_name:
                        mapped_sid = school_record['sid']
                        school_name_mapping[original_sid] = mapped_sid
                        print(f"  通过学校名字匹配成功：{school_name} -> {mapped_sid}")
                        name_found = True
                        break
            
            if not name_found:
                # 分配特定ID
                if original_sid not in school_name_mapping:
                    school_name_mapping[original_sid] = unknown_school_counter
                    mapped_sid = unknown_school_counter
                    unknown_school_counter += 1
                    print(f"  学校ID {original_sid} ({school_name}) 未找到匹配，分配新ID: {mapped_sid}")
                    school_not_found.append(original_sid)
                else:
                    mapped_sid = school_name_mapping[original_sid]
                    print(f"  使用已分配的ID: {mapped_sid}")
    
    print(f"\n=== 匹配结果 ===")
    print(f"未找到匹配的学校: {school_not_found}")
    print(f"名字匹配映射: {school_name_mapping}")
    
    return school_not_found, school_name_mapping

if __name__ == "__main__":
    try:
        test_school_matching()
    except Exception as e:
        print(f"测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
