## 项目启动

导入项目到 pycharm 中，安装依赖包，运行项目即可。

> 使用以下 `pip install -r requirements.txt` 安装依赖包


## 代码结构

```
data -- 省份以及结构数据
getRealID -- 根据院校代码表格生成每个省份对应院校在系统中的数据
tools -- 工具类
RealData -- 爬取好的一些数据 因为有一部分可以充分利用 保存起来
2021-2023_major -- 获取21-23年的院校专业最低投档线
get_plan -- 获取24年专业招生计划
format -- 格式化爬取数据（我这里没有重构完代码，周某该好重新发这个文件，现在只能格式化不能针对专业组分类）
ten2group -- 某些身份需要10条为一组
```

## 2021-2023_major 参数解释

```
    PROVINCE_ID: int = 51  // 省份对应ID， 看 ./Data 里面的为准
    YEAR: int = 2021  // 年份
    TYPE_ID: int = 0
    LOCAL_BATCH_ID: int = 44
    FILE_NAME: str = "getRealID/finish/四川本科匹配成功的数据.xlsx"  // 爬取的院校名单
    MAX_RETRIES: int = 3    // 最大重试次数
    RETRY_ATTEMPTS: int = 2  // 重试次数
    PROXY: Dict[str, str] = None
```

## format 参数

```
    files = {
        'recruit_plan': "./RealData/41_0_2024_41_get_recruit_plan_.xlsx",
        'score_2023': "./RealData/41_0_2023_41_get_major_score_line_.xlsx",
        'score_2022': "./RealData/41_0_2022_41_get_major_score_line_.xlsx",
        'score_2021': "./RealData/41_0_2021_41_get_major_score_line_.xlsx"
    }
    
    就爬取完的四个文件
```

## 问题

![image-20250305215122491](https://qiniu.waite.wang/202503052151614.png)

> 如果你遇到你这个报错，是因为你本地没有配置代理/ 代理池，到 `tools/response.py` 把 proxy 相关的注释掉或者改成你的代理端口

> 如何注释？
>
> 全局替换  `res = requests.get(url, headers=get_headers(), proxies=proxy, timeout=10).json()`
>
> 为
>
> ```
> res = requests.get(url, headers=get_headers(), timeout=10).json()
> ```