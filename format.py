import pandas as pd
from typing import Dict, List, Tuple
from tqdm import tqdm  # For progress bars


def load_data_files() -> <PERSON><PERSON>[pd.DataFrame, ...]:
    """Load all required Excel files"""
    print("\n正在加载Excel文件...")
    files = {
        'recruit_plan': "./new_data_recruit_plan/41_0_2025_44_get_recruit_plan.xlsx",
        'score_2023': "./new_data/41_0_2024_44_get_major_score_line.xlsx",
        'score_2022': "./new_data/41_0_2023_44_get_major_score_line.xlsx",
        'score_2021': "./new_data/41_0_2022_44_get_major_score_line.xlsx"
    }
    return tuple(pd.read_excel(file) for file in tqdm(files.values(), desc="加载文件"))


def get_school_data() -> List[Dict]:
    """Get combined school data with both ID mappings and names"""
    print("\n正在获取学校数据...")
    school_df = pd.read_excel("getRealID/finish/广东本科匹配成功的数据.xlsx")
    return school_df[['sid', 'old_sid', 'school_name']].to_dict('records')


def filter_by_type(dataframes: Tuple[pd.DataFrame, ...]) -> Tuple[pd.DataFrame, ...]:
    """Filter dataframes by type_id"""
    print("\n正在过滤数据...")
    # type_ids = ['2_8_0', '2_7_0']
    type_ids = ['2073_14_0','2_8_0', '2_7_0']
    return tuple(df[df['type_id'].isin(type_ids)] for df in dataframes)


def clean_text(df: pd.DataFrame) -> pd.DataFrame:
    """Clean text in DataFrame columns"""
    print("\n正在清理文本数据...")
    text_columns = ['sg_name', 'spname', 'sp_info', 'sg_info']

    for col in tqdm(text_columns, desc="清理列"):
        if col in df.columns:
            try:
                if col == 'spname':
                    df[col] = df[col].str.replace('（', '(').str.replace('）', ')')
                else:
                    df[col] = df[col].str.replace('（', '').str.replace('）', '')
                    if col in ['sp_info', 'sg_info']:
                        df[col] = df[col].str.replace('首选物理，再选', '').str.replace('首选历史，再选', '')
            except AttributeError:
                continue
    return df


def process_major_data(major_data: Dict, score_data: List[pd.DataFrame], old_sid: str) -> Dict:
    """Process individual major data"""
    major_data['spname'] = major_data['spname'].replace('（', '(').replace('）', ')')
    major_name = major_data['spname'].split('(')[0]

    result = {
        "school_id": old_sid,
        "school_name": major_data['school_name'],
        "sg_name": major_data['sg_name'],
        "spname": major_data['spname'],
        "num": major_data['num'],
        "tuition": major_data['tuition'],
        "sg_info": major_data['sg_info']
    }

    print(result)
    print(major_name)

    years = [('', score_data[0]), ('_2022', score_data[1]), ('_2021', score_data[2])]
    for year_suffix, year_data in years:
        found = False
        for score in year_data.to_dict('records'):
            if major_name == score['spname'].split('(')[0]:
                result[f'min{year_suffix}'] = score['min']
                result[f'min_section{year_suffix}'] = score['min_section']

                return result

            if major_name in score['spname']:
                result[f'min{year_suffix}'] = score['min']
                result[f'min_section{year_suffix}'] = score['min_section']
                return result

        if not found:
            result[f'min{year_suffix}'] = '-'
            result[f'min_section{year_suffix}'] = '-'

    return result


def main():
    print("\n=== 开始处理数据 ===")

    # Load data
    recruit_plan, *score_lines = load_data_files()
    school_data = get_school_data()
    print(f"获取到 {len(school_data)} 所学校的数据")

    # Filter and clean data
    recruit_plan, *score_lines = filter_by_type((recruit_plan, *score_lines))
    recruit_plan = recruit_plan[
        ['school_id', 'school_name', 'sg_name', 'spname', 'num', 'tuition', 'sg_info', 'sp_info']]
    recruit_plan = clean_text(recruit_plan)

    # Process data
    print("\n正在处理专业数据...")
    DATA = []
    for school in tqdm(school_data, desc="处理学校"):
        school_recruit = recruit_plan[recruit_plan['school_id'] == school['sid']]
        if not school_recruit.empty:
            score_data = [df[df['school_id'] == school['sid']] for df in score_lines]
            for major in school_recruit.to_dict('records'):
                major_info = process_major_data(major, score_data, school['old_sid'])
                DATA.append(major_info)

    # Create final DataFrame and save
    print("\n正在保存数据...")
    result_df = pd.DataFrame(DATA)
    result_df['province'] = '广东省'
    result_df.to_excel("广东省-18-2023-1-2.xlsx", index=False)

    print(f"\n=== 处理完成 ===")
    print(f"总共处理了 {len(DATA)} 条数据")
    print(f"数据已保存至：广东省-18-2023-1-2.xlsx")


if __name__ == "__main__":
    main()
