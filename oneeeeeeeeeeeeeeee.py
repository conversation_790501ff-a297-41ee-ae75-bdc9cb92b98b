import pandas as pd

readSchoolId = pd.read_excel('./getRealID/finish/贵州省本科匹配成功的数据.xlsx')

readSchoolDict = {}

for index, row in readSchoolId.iterrows():
    readSchoolDict[row['school_name']] = row['old_sid']

print(readSchoolDict)


data = pd.read_excel('processed_results.xlsx')

print(data.columns)

print(data.head())

data['school_id'] = data['school_name'].map(readSchoolDict)

data_dict = data.to_dict(orient='records')
DATA = []

for i in data_dict:
    one_dict = {}
    one_dict['school_id'] = i['school_id']
    one_dict['school_name'] = i['school_name']
    one_dict['sp_info'] = i['sp_info']
    one_dict['专业代码'] = ''
    one_dict['spname'] = i['spname_copy']
    one_dict['tuition'] = str(i['num']) + '/' + str(i['tuition']).replace('.0', '') + '/' + str(i['2024_num']) + '/' + str(i['2023_num']) + '/' + str(i['2022_num'])
    one_dict['2024_min'] = str(i['2024_min']) + '/' + str(i['2024_min_section'])
    one_dict['2024_min-1'] = str(i['2024_min'])
    one_dict['2023_min'] = str(i['2023_min']) + '/' + str(i['2023_min_section'])
    one_dict['2022_min'] = str(i['2022_min']) + '/' + str(i['2022_min_section'])
    one_dict['province'] = '贵州省'
    one_dict['0'] = '1'
    one_dict['1'] = '2'
    one_dict['year'] = '2024'
    DATA.append(one_dict)

print(DATA)

df = pd.DataFrame(DATA)

df.to_excel('贵州省-26-2024-1-2-1.xlsx', index=False)
