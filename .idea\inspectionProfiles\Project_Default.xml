<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="107" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="numba" />
            <item index="2" class="java.lang.String" itemvalue="torch" />
            <item index="3" class="java.lang.String" itemvalue="numpy" />
            <item index="4" class="java.lang.String" itemvalue="SimpleITK" />
            <item index="5" class="java.lang.String" itemvalue="opencv-python" />
            <item index="6" class="java.lang.String" itemvalue="matplotlib" />
            <item index="7" class="java.lang.String" itemvalue="torchvision" />
            <item index="8" class="java.lang.String" itemvalue="Pillow" />
            <item index="9" class="java.lang.String" itemvalue="django_apscheduler" />
            <item index="10" class="java.lang.String" itemvalue="feapder" />
            <item index="11" class="java.lang.String" itemvalue="ddddocr" />
            <item index="12" class="java.lang.String" itemvalue="PyExecJS" />
            <item index="13" class="java.lang.String" itemvalue="cffi" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E402" />
          <option value="W191" />
          <option value="E101" />
          <option value="W605" />
          <option value="W292" />
          <option value="E114" />
          <option value="E111" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="rest_framework_simplejwt.tokens.Token.access_token" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="SqlNoDataSourceInspection" enabled="false" level="WARNING" enabled_by_default="false" />
  </profile>
</component>