# -*- coding: utf-8 -*-
"""
@author: Waite
@time: 2023-04-30 23:10
@file: 2022_major.py
@desc:
"""
import logging
import time
import random
from typing import List, Dict, Any

import pandas as pd
from tqdm import tqdm

from tools.response import get_professional_score_line, get_recruit_plan, get_province_score_line_new, \
  get_major_score_line, get_province_score_line, get_recruit_plan_new

# 参数列表
PROVINCE_ID = 51
YEAR = 2023
TYPE_ID = 0
# 7 - 本科一批
# 8 - 本科二批
# 10 - 专科批
local_batch_id = 44
FILE_NAME = "getRealID/finish/四川本科匹配成功的数据.xlsx"

# 代理设置
proxy = {
  'http': 'http://127.0.0.1:3060',
  'https': 'http://127.0.0.1:3060'
}

# 读取学校列表
school_data = pd.read_excel(FILE_NAME)[['sid', 'school_name']]

# 抓取school_id, school_name
school_list = [{i: j} for i, j in
               zip(school_data['sid'].tolist(), school_data['school_name'].tolist())]

# with open('./data/school_dic_zhuanke.json', 'r', encoding='utf-8') as f:
#   school_list = eval(f.read())

# print(school_list)
#
DATA = []
error_school_list = []

# # print(school_list)
# school_list = {293: "广州大学"}
TIME = ''
none = []

if none:
  school_dict = []
  for i in none:
    school_id = i
    school_name = ""
    for j in school_list:
      if j.__contains__(school_id):
        school_name = j[school_id]
        break
    school_dict.append({school_id: school_name})
  school_list = school_dict
  print('school_list', school_list)


def fetch_school_data(school_id: int, school_name: str, max_retries: int = 3) -> tuple[list, bool]:
  """
  获取学校数据，包含重试机制
  返回: (data_list, success_status)
  """
  for attempt in range(max_retries):
    try:
      res = get_major_score_line(PROVINCE_ID, YEAR, school_id, school_name)

      if res['code'] == 200:
        return res['data'], True

      if res['code'] == 403:
        # 403错误时随机等待10-15秒
        sleep_time = random.uniform(10, 15)
        time.sleep(sleep_time)
        continue

      # 其他错误随机等待2-5秒
      sleep_time = random.uniform(2, 5)
      time.sleep(sleep_time)

    except Exception as e:
      logging.error(f"Error fetching data for school {school_name}(id:{school_id}): {str(e)}")
      sleep_time = random.uniform(2, 5)
      time.sleep(sleep_time)

  return [], False


def process_school_list(school_list: List[Dict[int, str]]) -> tuple[list, list]:
  """
  处理学校列表，返回成功获取的数据和失败的学校列表
  """
  data = []
  error_schools = []

  for school_info in tqdm(school_list):
    school_id = list(school_info.keys())[0]
    school_name = school_info[school_id]

    school_data, success = fetch_school_data(school_id, school_name)

    if success:
      data.extend(school_data)
    else:
      error_schools.append(school_info)
      logging.warning(f"Failed to fetch data for school {school_name}(id:{school_id})")

  return data, error_schools


# 主执行逻辑
def main():
  all_data = []
  current_school_list = school_list

  # 最多重试整个失败列表2次
  for retry_count in range(2):
    data, error_schools = process_school_list(current_school_list)
    all_data.extend(data)

    if not error_schools:
      break

    logging.info(f"Retry #{retry_count + 1} for {len(error_schools)} failed schools")
    current_school_list = error_schools
    time.sleep(random.uniform(5, 10))  # 重试前等待

  # 输出最终仍然失败的学校
  if error_schools:
    logging.warning("Schools that failed after all retries:")
    for school in error_schools:
      school_id = list(school.keys())[0]
      logging.warning(f"Failed school ID: {school_id}, Name: {school[school_id]}")

  # 保存数据
  pd.DataFrame(all_data).to_excel(
    f"{PROVINCE_ID}_{TYPE_ID}_{YEAR}_{local_batch_id}_get_major_score_line_{TIME}.xlsx"
  )


if __name__ == "__main__":
  main()
